package service

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/logic"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetPostListForAdmin 获取帖子列表（管理端）
func (s *Service) GetPostListForAdmin(req *define.GetPostAdminListReq) (*define.GetPostAdminListResp, error) {
	postSchema := repo.GetQuery().Post
	queryBuilder := search.NewQueryBuilder()

	// 构建查询条件
	// 1. 时间范围筛选
	if !req.StartTime.IsZero() {
		queryBuilder.Gte(postSchema.CreatedAt, req.StartTime)
	}
	if !req.EndTime.IsZero() {
		queryBuilder.Lte(postSchema.CreatedAt, req.EndTime)
	}

	// 2. 帖子ID筛选
	if req.PostID != "" {
		queryBuilder.Eq(postSchema.ID, req.PostID)
	}

	// 3. 用户ID筛选
	if req.UserID != "" {
		queryBuilder.Eq(postSchema.MerchantID, req.UserID)
	}

	// 4. 状态筛选
	if req.Status != 0 {
		queryBuilder.Eq(postSchema.Status, int32(req.Status))
	}

	// 5. 关键字搜索（搜索描述）
	if req.Keyword != "" {
		queryBuilder.Like(postSchema.Description, "%"+req.Keyword+"%")
	}

	// 默认按创建时间倒序排列
	queryBuilder.OrderByDesc(postSchema.CreatedAt)

	queryWrapper := queryBuilder.Build()

	// 分页查询
	posts, total, err := repo.NewPostRepo(postSchema.WithContext(s.ctx)).SelectPage(
		queryWrapper,
		req.GetPage(),
		req.GetPageSize(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询管理端帖子列表失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 如果没有数据，直接返回空列表
	if len(posts) == 0 {
		return &define.GetPostAdminListResp{
			List:  []*define.GetPostAdminListData{},
			Total: total,
		}, nil
	}

	// 收集商家ID
	merchantIDs := make([]string, 0, len(posts))
	for _, post := range posts {
		merchantIDs = append(merchantIDs, post.MerchantID)
	}

	// 批量获取商家信息
	userMap, err := facade.GetNewUserMap(s.ctx, merchantIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量获取商家信息失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 构建响应数据
	list := make([]*define.GetPostAdminListData, 0, len(posts))
	for _, post := range posts {
		// 获取媒体文件
		mediaFiles, err := post.GetMediaFiles()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取帖子媒体文件失败 postID:%s, err:%v", post.ID, err)
			mediaFiles = []define.MediaFile{}
		}

		// 获取商家信息
		var merchantName string
		if userInfo, exists := userMap[post.MerchantID]; exists {
			merchantName = userInfo.Nickname
		}

		listData := &define.GetPostAdminListData{
			ID:           post.ID,
			MerchantID:   post.MerchantID,
			MerchantName: merchantName,
			Description:  post.Description,
			Price:        post.Price,
			MediaFiles:   mediaFiles,
			Status:       post.GetStatus(),
			CreatedAt:    post.CreatedAt,
		}

		list = append(list, listData)
	}

	return &define.GetPostAdminListResp{
		List:  list,
		Total: total,
	}, nil
}

// GetPostDetailForAdmin 获取帖子详情（管理端）
func (s *Service) GetPostDetailForAdmin(req *define.GetPostAdminDetailReq) (*define.GetPostAdminDetailResp, error) {
	postSchema := repo.GetQuery().Post

	queryWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, req.ID).
		Build()

	post, err := repo.NewPostRepo(postSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, define.CC500001Err
		}
		log.Ctx(s.ctx).Errorf("查询管理端帖子详情失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 获取媒体文件
	mediaFiles, err := post.GetMediaFiles()
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取媒体文件失败: %v", err)
		mediaFiles = []define.MediaFile{}
	}

	// 获取商家信息
	userInfo, err := facade.GetNewUser(s.ctx, post.MerchantID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取商家信息失败 merchantID:%s, err:%v", post.MerchantID, err)
		return nil, commondefine.CommonErr
	}

	return &define.GetPostAdminDetailResp{
		ID:           post.ID,
		MerchantID:   post.MerchantID,
		MerchantName: userInfo.Nickname,
		Description:  post.Description,
		Price:        post.Price,
		MediaFiles:   mediaFiles,
		Status:       post.GetStatus(),
		CreatedAt:    post.CreatedAt,
		UpdatedAt:    post.UpdatedAt,
	}, nil
}

// UpdatePostStatusForAdmin 更新帖子状态（管理端）
func (s *Service) UpdatePostStatusForAdmin(req *define.UpdatePostStatusAdminReq) (*define.UpdatePostStatusAdminResp, error) {
	// 管理端只能设置为违规下架状态
	if req.Status != enums.PostStatusViolation {
		return nil, define.CC500002Err.SetMsg("管理端只能将帖子状态设为违规下架")
	}

	postSchema := repo.GetQuery().Post

	// 查询帖子是否存在
	queryWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, req.ID).
		Build()

	post, err := repo.NewPostRepo(postSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, define.CC500001Err
		}
		log.Ctx(s.ctx).Errorf("查询帖子失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 管理端状态更新业务规则检查
	currentStatus := post.GetStatus()

	// 已删除(-1)的帖子不能变更状态
	if currentStatus == enums.PostStatusDeleted {
		return nil, define.CC500010Err.SetMsg("已删除(-1)的帖子不能变更状态")
	}

	// 更新状态为违规下架
	updateData := map[string]any{
		"status": int32(enums.PostStatusViolation),
	}

	updateWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, req.ID).
		Build()

	err = repo.NewPostRepo(postSchema.WithContext(s.ctx)).UpdateField(updateData, updateWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("更新帖子状态失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 同步清理缓存（确保状态更新后立即清理缓存）
	if err := logic.InvalidatePostCache(s.ctx, req.ID); err != nil {
		log.Ctx(s.ctx).Warnf("清理帖子缓存失败: %v", err)
	}

	return &define.UpdatePostStatusAdminResp{
		ID: req.ID,
	}, nil
}
